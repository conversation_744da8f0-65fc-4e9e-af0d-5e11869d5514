/**
 * G6自定义字段节点注册
 * 用于注册卡片式表节点，支持字段列表显示
 */

import { Rect, register, ExtensionCategory } from '@antv/g6'
import type { LineageNode, TableInfo } from '@/types/lineage'
import { getFieldTypeColor } from './graphDataTransform'

/**
 * 节点样式配置
 */
export interface NodeStyleConfig {
  // 节点基础样式
  backgroundColor: string;
  borderColor: string;
  borderWidth: number;
  borderRadius: number;
  shadowColor: string;
  shadowBlur: number;
  shadowOffsetX: number;
  shadowOffsetY: number;

  // 标题样式
  headerBackgroundColor: string;
  headerTextColor: string;
  headerFontSize: number;
  headerFontWeight: string;
  headerPadding: number;

  // 字段样式
  fieldTextColor: string;
  fieldFontSize: number;
  fieldLineHeight: number;
  fieldPadding: number;
  fieldHoverBackgroundColor: string;
  fieldActiveBackgroundColor: string;

  // 类型标签样式
  typeTagBackgroundColor: string;
  typeTagTextColor: string;
  typeTagFontSize: number;
  typeTagPadding: number;
  typeTagBorderRadius: number;
}

/**
 * 默认节点样式
 */
export const DEFAULT_NODE_STYLE: NodeStyleConfig = {
  backgroundColor: '#ffffff',
  borderColor: '#d9d9d9',
  borderWidth: 1,
  borderRadius: 6,
  shadowColor: 'rgba(0, 0, 0, 0.1)',
  shadowBlur: 8,
  shadowOffsetX: 0,
  shadowOffsetY: 2,

  headerBackgroundColor: '#fafafa',
  headerTextColor: '#262626',
  headerFontSize: 14,
  headerFontWeight: 'bold',
  headerPadding: 12,

  fieldTextColor: '#595959',
  fieldFontSize: 12,
  fieldLineHeight: 20,
  fieldPadding: 8,
  fieldHoverBackgroundColor: '#f0f0f0',
  fieldActiveBackgroundColor: '#e6f7ff',

  typeTagBackgroundColor: '#f0f0f0',
  typeTagTextColor: '#666666',
  typeTagFontSize: 10,
  typeTagPadding: 2,
  typeTagBorderRadius: 2
}

/**
 * 自定义表节点类
 */
class TableNode extends Rect {
  // 获取节点数据
  get nodeData(): any {
    return this.context.graph.getNodeData(this.id).data || {}
  }

  // 获取表信息
  get tableInfo(): TableInfo {
    return this.nodeData.tableInfo || { name: '', type: 'table' as const, fields: [] }
  }

  // 获取字段列表
  get fields(): LineageNode[] {
    return this.nodeData.fields || []
  }

  // 计算节点尺寸
  getNodeSize(attributes: any): [number, number] {
    const style = { ...DEFAULT_NODE_STYLE, ...attributes }
    const width = 240 // 固定宽度
    const height = calculateNodeHeight(this.fields, style)
    return [width, height]
  }

  // 获取表头样式
  getHeaderStyle(attributes: any) {
    const [width] = this.getNodeSize(attributes)
    const style = { ...DEFAULT_NODE_STYLE, ...attributes }

    return {
      x: -width / 2,
      y: -this.getNodeSize(attributes)[1] / 2,
      width: width,
      height: style.headerFontSize + style.headerPadding * 2,
      fill: style.headerBackgroundColor,
      stroke: style.borderColor,
      lineWidth: style.borderWidth,
      radius: [style.borderRadius, style.borderRadius, 0, 0]
    }
  }

  // 绘制表头
  drawHeaderShape(attributes: any, container: any) {
    const headerStyle = this.getHeaderStyle(attributes)
    this.upsert('header', 'rect', headerStyle, container)
  }

  // 获取表名文本样式
  getTableNameStyle(attributes: any) {
    const [width] = this.getNodeSize(attributes)
    const style = { ...DEFAULT_NODE_STYLE, ...attributes }

    return {
      x: 0,
      y: -this.getNodeSize(attributes)[1] / 2 + style.headerPadding + style.headerFontSize / 2,
      text: this.tableInfo.name,
      fontSize: style.headerFontSize,
      fontWeight: style.headerFontWeight,
      fill: style.headerTextColor,
      textAlign: 'center',
      textBaseline: 'middle'
    }
  }

  // 绘制表名
  drawTableNameShape(attributes: any, container: any) {
    const tableNameStyle = this.getTableNameStyle(attributes)
    this.upsert('tableName', 'text', tableNameStyle, container)
  }

  // 获取字段容器样式
  getFieldsContainerStyle(attributes: any) {
    const [width, height] = this.getNodeSize(attributes)
    const style = { ...DEFAULT_NODE_STYLE, ...attributes }
    const headerHeight = style.headerFontSize + style.headerPadding * 2

    return {
      x: -width / 2,
      y: -height / 2 + headerHeight,
      width: width,
      height: height - headerHeight,
      fill: style.backgroundColor,
      stroke: style.borderColor,
      lineWidth: style.borderWidth,
      radius: [0, 0, style.borderRadius, style.borderRadius]
    }
  }

  // 绘制字段容器
  drawFieldsContainerShape(attributes: any, container: any) {
    const fieldsContainerStyle = this.getFieldsContainerStyle(attributes)
    this.upsert('fieldsContainer', 'rect', fieldsContainerStyle, container)
  }

  // 绘制字段列表
  drawFieldsShape(attributes: any, container: any) {
    const [width, height] = this.getNodeSize(attributes)
    const style = { ...DEFAULT_NODE_STYLE, ...attributes }
    const headerHeight = style.headerFontSize + style.headerPadding * 2

    this.fields.forEach((field, index) => {
      const fieldY = -height / 2 + headerHeight + style.fieldPadding + (index + 0.5) * style.fieldLineHeight

      // 字段名
      const fieldNameStyle = {
        x: -width / 2 + 8,
        y: fieldY,
        text: field.fieldName,
        fontSize: style.fieldFontSize,
        fill: style.fieldTextColor,
        textAlign: 'left',
        textBaseline: 'middle'
      }
      this.upsert(`fieldName-${index}`, 'text', fieldNameStyle, container)

      // 数据类型
      if (field.dataType) {
        const typeText = formatDataType(field.dataType)
        const typeColor = getFieldTypeColor(field.dataType.type)

        const fieldTypeStyle = {
          x: width / 2 - 8,
          y: fieldY,
          text: typeText,
          fontSize: style.typeTagFontSize,
          fill: typeColor,
          textAlign: 'right',
          textBaseline: 'middle'
        }
        this.upsert(`fieldType-${index}`, 'text', fieldTypeStyle, container)
      }

      // 主键标识
      if (field.isKey) {
        const keyIconStyle = {
          x: -width / 2 + field.fieldName.length * style.fieldFontSize * 0.6 + 12,
          y: fieldY,
          text: '🔑',
          fontSize: style.fieldFontSize,
          textAlign: 'left',
          textBaseline: 'middle'
        }
        this.upsert(`keyIcon-${index}`, 'text', keyIconStyle, container)
      }
    })
  }

  // 主渲染方法
  render(attributes: any, container: any) {
    // 设置节点尺寸
    const [width, height] = this.getNodeSize(attributes)
    attributes.size = [width, height]

    // 不调用父类的render，因为我们要完全自定义
    // super.render(attributes, container)

    // 绘制表头
    this.drawHeaderShape(attributes, container)

    // 绘制表名
    this.drawTableNameShape(attributes, container)

    // 绘制字段容器
    this.drawFieldsContainerShape(attributes, container)

    // 绘制字段列表
    this.drawFieldsShape(attributes, container)
  }
}

/**
 * 注册表节点类型
 * @param styleCfg 样式配置
 */
export function registerTableNode(styleCfg: Partial<NodeStyleConfig> = {}) {
  const style = { ...DEFAULT_NODE_STYLE, ...styleCfg }

  // 注册自定义节点
  register(ExtensionCategory.NODE, 'table-node', TableNode)

  console.log('Table node registered successfully with style:', style)

  return TableNode
}

/**
 * 计算节点高度
 * @param fields 字段列表
 * @param style 样式配置
 * @returns 节点高度
 */
export function calculateNodeHeight(fields: LineageNode[], style: NodeStyleConfig): number {
  const headerHeight = style.headerFontSize + style.headerPadding * 2
  const fieldsHeight = fields.length * style.fieldLineHeight + style.fieldPadding * 2
  return headerHeight + fieldsHeight + 20 // 额外的底部间距
}

/**
 * 格式化数据类型显示
 * @param dataType 数据类型对象
 * @returns 格式化后的类型字符串
 */
export function formatDataType(dataType: any): string {
  if (!dataType || !dataType.type) return ''

  let result = dataType.type

  if (dataType.length) {
    result += `(${dataType.length})`
  } else if (dataType.precision && dataType.scale) {
    result += `(${dataType.precision},${dataType.scale})`
  } else if (dataType.precision) {
    result += `(${dataType.precision})`
  }

  return result
}
